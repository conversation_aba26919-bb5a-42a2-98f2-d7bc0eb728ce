import { Injectable } from '@nestjs/common'
import { app } from 'electron'
import { AutoUpdater } from '../../app-modules/AutoUpdater.js'
import { UpdateCheckResult, UpdateStartResult } from '@app/shared/types/ipc/auto-updater.js'

@Injectable()
export class AutoUpdaterService {
  
  /**
   * 检查更新
   */
  async checkForUpdates(): Promise<UpdateCheckResult> {
    const updater = new AutoUpdater()
    const result = await updater.getAutoUpdater().checkForUpdates()
    
    if (result?.updateInfo?.version && result.updateInfo.version !== app.getVersion()) {
      return { hasUpdate: true, version: result.updateInfo.version }
    }
    
    return { hasUpdate: false }
  }

  /**
   * 开始更新
   */
  async startUpdate(): Promise<UpdateStartResult> {
    const updater = new AutoUpdater()
    // await updater.runAutoUpdater()
    // 可选：下载完成后自动安装
    updater.getAutoUpdater().quitAndInstall()
    return { started: true }
  }
}
