import { app } from 'electron'
import { AppModule } from '../infra/types/AppModule.js'
import electronUpdater, { type AppUpdater, type Logger } from 'electron-updater'
import path from 'path'

type DownloadNotification = Parameters<AppUpdater['checkForUpdatesAndNotify']>[0]

export class AutoUpdater implements AppModule {

  readonly #logger: Logger | null
  readonly #notification: DownloadNotification

  constructor(
    {
      logger = null,
      downloadNotification = undefined,
    }:
    {
      logger?: Logger | null | undefined
      downloadNotification?: DownloadNotification
    } = {},
  ) {
    this.#logger = logger
    this.#notification = downloadNotification
  }

  async enable(): Promise<void> {
    await this.runAutoUpdater()
  }

  getAutoUpdater(): AppUpdater {
    // Using destructuring to access autoUpdater due to the CommonJS module of 'electron-updater'.
    // It is a workaround for ESM compatibility issues, see https://github.com/electron-userland/electron-builder/issues/7976.
    const { autoUpdater } = electronUpdater
    return autoUpdater
  }

  async runAutoUpdater() {
    const updater = this.getAutoUpdater()
    
    updater.logger = this.#logger || console
    updater.fullChangelog = true

    updater.allowPrerelease = true

    if (import.meta.env.VITE_DISTRIBUTION_CHANNEL) {
      updater.channel = import.meta.env.VITE_DISTRIBUTION_CHANNEL
    }
    
    try {
      return await updater.checkForUpdatesAndNotify(this.#notification)
    }
    catch (error) {
      if (error instanceof Error) {
        if (error.message.includes('No published versions')) {
          return null
        }
      }

      throw error
    }
  }
}

export function autoUpdater(...args: ConstructorParameters<typeof AutoUpdater>) {
  return new AutoUpdater(...args)
}
