import { Global, Module } from '@nestjs/common'
import { RequestService } from './request.service.js'
import { NestDatabaseService } from './database.service.js'
import { BaseInfoIPCHandler } from './base-info.ipc-handler.js'
import { AutoUpdaterIPCHandler } from './auto-updater.ipc-handler.js'
import { AutoUpdaterService } from './auto-updater.service.js'

@Module({
  providers: [
    BaseInfoIPCHandler,
    RequestService,
    AutoUpdaterService,
    AutoUpdaterIPCHandler,
    {
      provide: NestDatabaseService,
      useFactory: () => new NestDatabaseService('clipnest.db', 'migrations')
    }
  ],
  exports: [
    RequestService,
    NestDatabaseService,
  ]
})
@Global()
export class GlobalModule {}
